const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BACKEND_URL = 'http://localhost:3001/api/v1';
const FRONTEND_URL = 'http://localhost:3000';

async function testFullStackIntegration() {
  console.log('🔗 Testing Full Stack Integration...\n');
  
  const testEmail = `integration${Date.now()}@example.com`;
  let authToken = '';
  let userId = '';

  try {
    // Step 1: Test backend health
    console.log('1. Testing backend health...');
    const healthResponse = await fetch(`${BACKEND_URL.replace('/api/v1', '')}/health`);
    const healthData = await healthResponse.json();
    
    if (healthData.status === 'ok') {
      console.log('✅ Backend health check passed');
      console.log('   Services status:', Object.entries(healthData.services)
        .map(([service, status]) => `${service}: ${status}`)
        .join(', '));
    } else {
      throw new Error(`Backend health check failed: ${healthData.status}`);
    }

    // Step 2: Test frontend accessibility
    console.log('\n2. Testing frontend accessibility...');
    const frontendResponse = await fetch(FRONTEND_URL);
    
    if (frontendResponse.ok) {
      console.log('✅ Frontend is accessible');
      console.log('   Status:', frontendResponse.status);
    } else {
      throw new Error(`Frontend not accessible: ${frontendResponse.status}`);
    }

    // Step 3: Test CORS configuration
    console.log('\n3. Testing CORS configuration...');
    const corsTestResponse = await fetch(`${BACKEND_URL}/auth/register`, {
      method: 'OPTIONS',
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    if (corsTestResponse.ok) {
      console.log('✅ CORS configuration working');
    } else {
      console.log('⚠️ CORS may need configuration for frontend-backend communication');
    }

    // Step 4: Test complete authentication flow
    console.log('\n4. Testing complete authentication flow...');
    
    // Register user
    const registerResponse = await fetch(`${BACKEND_URL}/auth/register`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Origin': FRONTEND_URL
      },
      body: JSON.stringify({
        email: testEmail,
        password: 'testpassword123',
        name: 'Integration Test User'
      })
    });
    
    const registerData = await registerResponse.json();
    if (registerData.success) {
      authToken = registerData.data.token;
      userId = registerData.data.user.id;
      console.log('✅ User registration successful');
    } else {
      throw new Error(`Registration failed: ${registerData.error}`);
    }

    // Login user
    const loginResponse = await fetch(`${BACKEND_URL}/auth/login`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Origin': FRONTEND_URL
      },
      body: JSON.stringify({
        email: testEmail,
        password: 'testpassword123'
      })
    });
    
    const loginData = await loginResponse.json();
    if (loginData.success) {
      console.log('✅ User login successful');
      console.log('   Token received and valid');
    } else {
      throw new Error(`Login failed: ${loginData.error}`);
    }

    // Step 5: Test protected routes
    console.log('\n5. Testing protected routes...');
    
    const protectedRoutes = [
      { endpoint: '/users/me', name: 'User Profile' },
      { endpoint: '/users/me/stats', name: 'User Stats' },
      { endpoint: '/projects', name: 'Projects List' },
      { endpoint: '/users/me/activity', name: 'User Activity' }
    ];

    for (const route of protectedRoutes) {
      const response = await fetch(`${BACKEND_URL}${route.endpoint}`, {
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Origin': FRONTEND_URL
        }
      });
      
      const data = await response.json();
      if (data.success) {
        console.log(`✅ ${route.name} endpoint working`);
      } else {
        console.log(`❌ ${route.name} endpoint failed:`, data.error);
      }
    }

    // Step 6: Test project management workflow
    console.log('\n6. Testing project management workflow...');
    
    // Create project
    const createProjectResponse = await fetch(`${BACKEND_URL}/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
        'Origin': FRONTEND_URL
      },
      body: JSON.stringify({
        name: 'Integration Test Project',
        description: 'Testing full stack integration'
      })
    });
    
    const projectData = await createProjectResponse.json();
    let projectId = '';
    
    if (projectData.success) {
      projectId = projectData.data.id;
      console.log('✅ Project creation successful');
    } else {
      console.log('❌ Project creation failed:', projectData.error);
    }

    // Update project
    if (projectId) {
      const updateProjectResponse = await fetch(`${BACKEND_URL}/projects/${projectId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
          'Origin': FRONTEND_URL
        },
        body: JSON.stringify({
          name: 'Updated Integration Test Project',
          description: 'Updated via integration test'
        })
      });
      
      const updateData = await updateProjectResponse.json();
      if (updateData.success) {
        console.log('✅ Project update successful');
      } else {
        console.log('❌ Project update failed:', updateData.error);
      }
    }

    // Step 7: Test error handling
    console.log('\n7. Testing error handling...');
    
    // Test invalid token
    const invalidTokenResponse = await fetch(`${BACKEND_URL}/users/me`, {
      headers: { 
        'Authorization': 'Bearer invalid-token',
        'Origin': FRONTEND_URL
      }
    });
    
    const invalidTokenData = await invalidTokenResponse.json();
    if (!invalidTokenData.success && invalidTokenResponse.status === 401) {
      console.log('✅ Invalid token handling working');
    } else {
      console.log('❌ Invalid token handling failed');
    }

    // Test missing authorization
    const noAuthResponse = await fetch(`${BACKEND_URL}/users/me`, {
      headers: { 'Origin': FRONTEND_URL }
    });
    
    if (noAuthResponse.status === 401) {
      console.log('✅ Missing authorization handling working');
    } else {
      console.log('❌ Missing authorization handling failed');
    }

    // Step 8: Test data consistency
    console.log('\n8. Testing data consistency...');
    
    const statsResponse = await fetch(`${BACKEND_URL}/users/me/stats`, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'Origin': FRONTEND_URL
      }
    });
    
    const statsData = await statsResponse.json();
    if (statsData.success) {
      console.log('✅ Data consistency check passed');
      console.log('   User has projects:', statsData.data.totalProjects > 0 ? 'Yes' : 'No');
      console.log('   Token balance:', statsData.data.tokenBalance);
    }

    console.log('\n🎉 Full stack integration testing completed successfully!');
    console.log('\n📊 Integration Summary:');
    console.log('   ✅ Backend services healthy');
    console.log('   ✅ Frontend accessible');
    console.log('   ✅ CORS configured');
    console.log('   ✅ Authentication flow working');
    console.log('   ✅ Protected routes secured');
    console.log('   ✅ CRUD operations functional');
    console.log('   ✅ Error handling proper');
    console.log('   ✅ Data consistency maintained');

  } catch (error) {
    console.error('❌ Full stack integration test failed:', error.message);
  }
}

testFullStackIntegration();
