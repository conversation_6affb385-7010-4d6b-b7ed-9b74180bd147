import { BlobStringReply, DoubleReply, MapReply } from '../RESP/types';
declare function transformVLinksWithScoresReply(reply: any): Array<Record<string, DoubleReply>>;
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Get the connections for each layer of the HNSW graph with similarity scores
     * @param args - Same parameters as the VLINKS command
     * @see https://redis.io/commands/vlinks/
     */
    readonly parseCommand: (this: void, parser: import("../..").CommandParser, key: import("../RESP/types").RedisArgument, element: import("../RESP/types").RedisArgument) => void;
    readonly transformReply: {
        readonly 2: typeof transformVLinksWithScoresReply;
        readonly 3: () => Array<MapReply<BlobStringReply, DoubleReply>>;
    };
};
export default _default;
//# sourceMappingURL=VLINKS_WITHSCORES.d.ts.map