import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    /**
     * Constructs the SADD command
     *
     * @param parser - The command parser
     * @param key - The set key to add members to
     * @param members - One or more members to add to the set
     * @see https://redis.io/commands/sadd/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, members: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SADD.d.ts.map