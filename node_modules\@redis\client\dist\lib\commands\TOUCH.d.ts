import { CommandParser } from '../client/parser';
import { NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the TOUCH command to alter the last access time of keys
     *
     * @param parser - The command parser
     * @param key - One or more keys to touch
     * @returns The number of keys that were touched
     * @see https://redis.io/commands/touch/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=TOUCH.d.ts.map