import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the TYPE command to determine the data type stored at key
     *
     * @param parser - The command parser
     * @param key - Key to check
     * @returns String reply: "none", "string", "list", "set", "zset", "hash", "stream"
     * @see https://redis.io/commands/type/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply;
};
export default _default;
//# sourceMappingURL=TYPE.d.ts.map