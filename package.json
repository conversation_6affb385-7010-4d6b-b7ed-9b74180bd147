{"name": "i2d-convert", "version": "0.1.0", "description": "Image-to-Design-System Converter - Transform UI screenshots into production-ready design system assets", "main": "src/server/index.ts", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "tsx watch src/server/index.ts", "dev:worker": "tsx watch src/server/worker.ts", "dev:client": "vite", "dev:full": "concurrently \"npm run dev:server\" \"npm run dev:worker\" \"npm run dev:client\"", "start": "tsx src/server/index.ts", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "tsc -p tsconfig.server.json", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "setup": "npm install && npm run db:generate && npm run db:push && npm run db:seed", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["design-system", "ui-conversion", "ai", "machine-learning", "react", "typescript", "image-processing"], "author": "Kanousei Technology LLC", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/bull": "^3.15.9", "@types/jsonwebtoken": "^9.0.10", "autoprefixer": "^10.4.21", "aws-sdk": "^2.1498.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "form-data": "^4.0.4", "helmet": "^8.1.0", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "pg": "^8.16.3", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.7.1", "redis": "^5.7.0", "sharp": "^0.32.6", "tailwindcss": "^3.4.17", "uuid": "^9.0.1", "zod": "^4.0.14"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@vitejs/plugin-react": "^4.2.0", "concurrently": "^8.2.2", "prisma": "^5.7.1", "tsx": "^4.20.3", "typescript": "^5.3.2", "vite": "^5.0.5"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}