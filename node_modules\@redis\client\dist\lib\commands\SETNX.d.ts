import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the SETNX command
     *
     * @param parser - The command parser
     * @param key - The key to set if it doesn't exist
     * @param value - The value to set
     * @see https://redis.io/commands/setnx/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, value: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SETNX.d.ts.map