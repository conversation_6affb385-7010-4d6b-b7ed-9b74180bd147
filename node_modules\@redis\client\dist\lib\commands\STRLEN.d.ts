import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the STRLEN command to get the length of a string value
     *
     * @param parser - The command parser
     * @param key - The key holding the string value
     * @returns The length of the string value, or 0 when key does not exist
     * @see https://redis.io/commands/strlen/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=STRLEN.d.ts.map