import Redis from 'ioredis';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
}

interface SessionData {
  userId: string;
  email: string;
  role: string;
  lastActivity: number;
}

class RedisServiceClass {
  private client: Redis | null = null;
  private isConnected: boolean = false;

  async initialize(): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      
      this.client = new Redis(redisUrl, {
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      });

      // Test the connection
      await this.client.connect();
      await this.client.ping();
      
      this.isConnected = true;
      console.log('✓ Redis connection established successfully');

      // Set up event listeners
      this.client.on('error', (error) => {
        console.error('Redis error:', error);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('Redis connected');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        console.log('Redis disconnected');
        this.isConnected = false;
      });

    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      // Don't throw error in development - allow app to continue without Redis
      if (process.env.NODE_ENV === 'production') {
        throw error;
      }
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.quit();
      this.client = null;
      this.isConnected = false;
      console.log('Redis connection closed');
    }
  }

  get isReady(): boolean {
    return this.isConnected && this.client !== null;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.client || !this.isConnected) {
        return false;
      }

      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('Redis health check failed:', error);
      return false;
    }
  }

  // Session management
  async setSession(sessionId: string, data: SessionData, ttl: number = 86400): Promise<boolean> {
    try {
      if (!this.isReady) {
        console.warn('Redis not available, skipping session storage');
        return false;
      }

      const sessionKey = `session:${sessionId}`;
      await this.client!.setex(sessionKey, ttl, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Failed to set session:', error);
      return false;
    }
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      if (!this.isReady) {
        return null;
      }

      const sessionKey = `session:${sessionId}`;
      const data = await this.client!.get(sessionKey);
      
      if (!data) {
        return null;
      }

      return JSON.parse(data) as SessionData;
    } catch (error) {
      console.error('Failed to get session:', error);
      return null;
    }
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const sessionKey = `session:${sessionId}`;
      const result = await this.client!.del(sessionKey);
      return result > 0;
    } catch (error) {
      console.error('Failed to delete session:', error);
      return false;
    }
  }

  async refreshSession(sessionId: string, ttl: number = 86400): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const sessionKey = `session:${sessionId}`;
      const result = await this.client!.expire(sessionKey, ttl);
      return result === 1;
    } catch (error) {
      console.error('Failed to refresh session:', error);
      return false;
    }
  }

  // Caching methods
  async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const serializedValue = JSON.stringify(value);
      
      if (options.ttl) {
        await this.client!.setex(key, options.ttl, serializedValue);
      } else {
        await this.client!.set(key, serializedValue);
      }
      
      return true;
    } catch (error) {
      console.error('Failed to set cache:', error);
      return false;
    }
  }

  async get<T = any>(key: string): Promise<T | null> {
    try {
      if (!this.isReady) {
        return null;
      }

      const data = await this.client!.get(key);
      
      if (!data) {
        return null;
      }

      return JSON.parse(data) as T;
    } catch (error) {
      console.error('Failed to get cache:', error);
      return null;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const result = await this.client!.del(key);
      return result > 0;
    } catch (error) {
      console.error('Failed to delete cache:', error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const result = await this.client!.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Failed to check cache existence:', error);
      return false;
    }
  }

  // Rate limiting
  async incrementCounter(key: string, ttl: number = 3600): Promise<number> {
    try {
      if (!this.isReady) {
        return 0;
      }

      const multi = this.client!.multi();
      multi.incr(key);
      multi.expire(key, ttl);
      
      const results = await multi.exec();
      return results?.[0]?.[1] as number || 0;
    } catch (error) {
      console.error('Failed to increment counter:', error);
      return 0;
    }
  }

  // Utility methods
  async flushAll(): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      await this.client!.flushall();
      return true;
    } catch (error) {
      console.error('Failed to flush cache:', error);
      return false;
    }
  }

  async keys(pattern: string): Promise<string[]> {
    try {
      if (!this.isReady) {
        return [];
      }

      return await this.client!.keys(pattern);
    } catch (error) {
      console.error('Failed to get keys:', error);
      return [];
    }
  }
}

export const RedisService = new RedisServiceClass();
