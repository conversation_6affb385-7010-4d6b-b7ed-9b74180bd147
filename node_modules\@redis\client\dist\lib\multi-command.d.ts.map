{"version": 3, "file": "multi-command.d.ts", "sourceRoot": "", "sources": ["../../lib/multi-command.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAGtG,MAAM,MAAM,WAAW,GAAG;IACxB,OAAO,EAAE,SAAS,CAAC;IACnB,KAAK,EAAE,OAAO,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,WAAW,CAAC,CAAC;AAExD,MAAM,MAAM,cAAc,CAAC,CAAC,SAAS,UAAU,EAAE,OAAO,IAAI,CAAC,SAAS,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AAEzH,MAAM,WAAW,uBAAuB;IACtC,IAAI,EAAE,gBAAgB,CAAC;IACvB,cAAc,CAAC,EAAE,cAAc,CAAC;CACjC;AAED,MAAM,CAAC,OAAO,OAAO,iBAAiB;IACpC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAc;gBAE/B,WAAW,CAAC,EAAE,WAAW;IAIrC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,uBAAuB,CAAC,CAAM;IAEpD,QAAQ,CAAC,YAAY,cAAqB;IAE1C,UAAU,CAAC,IAAI,EAAE,gBAAgB,EAAE,cAAc,CAAC,EAAE,cAAc;IAOlE,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAE,cAAc,CAAC,EAAE,cAAc;IAmBtF,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;CAe7D"}