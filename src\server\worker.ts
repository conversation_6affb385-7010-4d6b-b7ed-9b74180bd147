import { QueueService } from './services/queue';
import { RedisService } from './services/redis';
import { DatabaseService } from './services/database';

class WorkerProcess {
  private isShuttingDown = false;

  async start(): Promise<void> {
    console.log('🔧 Starting I2D Convert Worker Process...\n');

    try {
      // Initialize services
      await this.initializeServices();

      // Set up graceful shutdown
      this.setupGracefulShutdown();

      console.log('✅ Worker process started successfully');
      console.log('📋 Listening for jobs...\n');

      // Keep the process alive
      this.keepAlive();

    } catch (error) {
      console.error('❌ Failed to start worker process:', error);
      process.exit(1);
    }
  }

  private async initializeServices(): Promise<void> {
    // Initialize database
    await DatabaseService.initialize();
    console.log('✓ Database initialized');

    // Initialize Redis
    await RedisService.initialize();
    console.log('✓ Redis initialized');

    // Initialize Queue service
    await QueueService.initialize();
    console.log('✓ Queue service initialized');
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) {
        console.log('Force shutdown...');
        process.exit(1);
      }

      this.isShuttingDown = true;
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);

      try {
        // Stop accepting new jobs and wait for current jobs to complete
        console.log('⏳ Waiting for current jobs to complete...');
        await QueueService.disconnect();
        console.log('✓ Queue service disconnected');

        await RedisService.disconnect();
        console.log('✓ Redis disconnected');

        console.log('✅ Worker process shut down gracefully');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon
  }

  private keepAlive(): void {
    // Log periodic status updates
    setInterval(async () => {
      if (!this.isShuttingDown) {
        try {
          const stats = await QueueService.getQueueStats();
          const redisHealth = await RedisService.healthCheck();
          
          console.log(`📊 Worker Status - Redis: ${redisHealth ? 'OK' : 'ERROR'}`);
          console.log(`   Queue Stats:`, JSON.stringify(stats, null, 2));
        } catch (error) {
          console.error('Error getting worker status:', error);
        }
      }
    }, 30000); // Every 30 seconds

    // Keep process alive
    setInterval(() => {
      if (this.isShuttingDown) {
        return;
      }
      // Just keep the event loop alive
    }, 1000);
  }
}

// Start the worker if this file is run directly
if (require.main === module) {
  const worker = new WorkerProcess();
  worker.start().catch((error) => {
    console.error('Failed to start worker:', error);
    process.exit(1);
  });
}

export default WorkerProcess;
