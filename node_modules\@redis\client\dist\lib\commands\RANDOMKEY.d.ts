import { CommandParser } from '../client/parser';
import { NumberReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the RANDOMKEY command
     *
     * @param parser - The command parser
     * @see https://redis.io/commands/randomkey/
     */
    readonly parseCommand: (this: void, parser: CommandParser) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=RANDOMKEY.d.ts.map