import { CommandParser } from '../client/parser';
import { NumberReply, RedisArgument } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the SREM command to remove one or more members from a set
     *
     * @param parser - The command parser
     * @param key - The key of the set to remove members from
     * @param members - One or more members to remove from the set
     * @returns The number of members that were removed from the set
     * @see https://redis.io/commands/srem/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, members: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SREM.d.ts.map