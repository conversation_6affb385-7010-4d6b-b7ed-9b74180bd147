import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    /**
     * Constructs the SDIFFSTORE command
     *
     * @param parser - The command parser
     * @param destination - The destination key to store the result
     * @param keys - One or more set keys to compute the difference from
     * @see https://redis.io/commands/sdiffstore/
     */
    readonly parseCommand: (this: void, parser: CommandParser, destination: RedisArgument, keys: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SDIFFSTORE.d.ts.map