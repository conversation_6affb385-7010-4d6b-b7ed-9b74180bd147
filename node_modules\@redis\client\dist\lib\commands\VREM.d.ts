import { CommandParser } from '../client/parser';
import { RedisArgument } from '../RESP/types';
declare const _default: {
    /**
     * Remove an element from a vector set
     *
     * @param parser - The command parser
     * @param key - The key of the vector set
     * @param element - The name of the element to remove from the vector set
     * @see https://redis.io/commands/vrem/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, element: RedisArgument) => void;
    readonly transformReply: {
        2: (reply: import("../RESP/types").NumberReply<0 | 1>) => boolean;
        3: () => import("../RESP/types").BooleanReply<boolean>;
    };
};
export default _default;
//# sourceMappingURL=VREM.d.ts.map