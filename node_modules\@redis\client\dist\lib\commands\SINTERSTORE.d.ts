import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the SINTERSTORE command
     *
     * @param parser - The command parser
     * @param destination - The destination key to store the result
     * @param keys - One or more set keys to compute the intersection from
     * @see https://redis.io/commands/sinterstore/
     */
    readonly parseCommand: (this: void, parser: CommandParser, destination: RedisArgument, keys: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SINTERSTORE.d.ts.map