import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the SRANDMEMBER command to get multiple random members from a set
     *
     * @param parser - The command parser
     * @param key - The key of the set to get random members from
     * @param count - The number of members to return. If negative, may return the same member multiple times
     * @see https://redis.io/commands/srandmember/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, count: number) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=SRANDMEMBER_COUNT.d.ts.map