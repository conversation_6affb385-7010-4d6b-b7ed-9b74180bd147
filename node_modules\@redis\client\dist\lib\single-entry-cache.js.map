{"version": 3, "file": "single-entry-cache.js", "sourceRoot": "", "sources": ["../../lib/single-entry-cache.ts"], "names": [], "mappings": ";;AAAA,MAAqB,gBAAgB;IACnC,OAAO,CAAK;IACZ,cAAc,CAAU;IAExB;;;;;;;;;OASG;IACH,GAAG,CAAC,MAAU;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,oBAAoB,EAAE,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;IAC3G,CAAC;IAED,GAAG,CAAC,MAAsB,EAAE,GAAM;QAChC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,oBAAoB,EAAE,CAAC,CAAC;IACvE,CAAC;CACF;AAtBD,mCAsBC;AAED,SAAS,oBAAoB;IAC3B,MAAM,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;IAC3B,OAAO,SAAS,SAAS,CAAC,CAAS,EAAE,KAAU;QAC7C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpB,OAAO,UAAU,CAAC;YACpB,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAA;AACH,CAAC"}