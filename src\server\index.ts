import app from './app'
import { S3Service } from './services/s3'
import { DatabaseService } from './services/database'
import { RedisService } from './services/redis'
import { QueueService } from './services/queue'

const PORT = process.env.PORT || 3001

async function startServer() {
  try {
    // Initialize database
    await DatabaseService.initialize()
    console.log('✓ Database initialized')

    // Initialize Redis service
    await RedisService.initialize()
    console.log('✓ Redis initialized')

    // Initialize Queue service
    await QueueService.initialize()
    console.log('✓ Queue service initialized')

    // Initialize S3 service
    await S3Service.initialize()
    console.log('✓ DigitalOcean Spaces initialized')

    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`)
      console.log(`Health check: http://localhost:${PORT}/health`)
      console.log(`API endpoints: http://localhost:${PORT}/api/v1`)
      console.log(`Upload endpoint: http://localhost:${PORT}/api/v1/images/upload`)
    })
  } catch (error) {
    console.error('Failed to start server:', error)
    process.exit(1)
  }
}

startServer()
