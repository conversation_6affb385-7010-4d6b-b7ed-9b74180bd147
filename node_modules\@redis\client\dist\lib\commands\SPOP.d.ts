import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the SPOP command to remove and return a random member from a set
     *
     * @param parser - The command parser
     * @param key - The key of the set to pop from
     * @see https://redis.io/commands/spop/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=SPOP.d.ts.map