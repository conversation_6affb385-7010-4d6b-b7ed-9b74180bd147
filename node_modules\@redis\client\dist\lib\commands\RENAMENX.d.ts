import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the RENAMENX command
     *
     * @param parser - The command parser
     * @param key - The key to rename
     * @param newKey - The new key name, if it doesn't exist
     * @see https://redis.io/commands/renamenx/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, newKey: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=RENAMENX.d.ts.map