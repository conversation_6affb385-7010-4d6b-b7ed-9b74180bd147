import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import path from 'path'
import { S3Service } from './services/s3'
import { DatabaseService } from './services/database'
import { RedisService } from './services/redis'
import { QueueService } from './services/queue'
import apiRoutes from './routes'

const app = express()

// Security middleware
app.use(helmet())

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
}))

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// API Routes
app.use('/api', apiRoutes)

// Legacy health check endpoint (will be moved to /api/health)
app.get('/health', async (req, res) => {
  const [s3Health, dbHealth, redisHealth, queueHealth] = await Promise.all([
    S3Service.healthCheck(),
    DatabaseService.healthCheck(),
    RedisService.healthCheck(),
    QueueService.healthCheck(),
  ])

  const overallStatus = s3Health && dbHealth && redisHealth && queueHealth ? 'ok' : 'degraded'

  res.json({
    status: overallStatus,
    timestamp: new Date().toISOString(),
    service: 'i2d-convert-api',
    services: {
      database: dbHealth ? 'ok' : 'error',
      s3: s3Health ? 'ok' : 'error',
      redis: redisHealth ? 'ok' : 'error',
      queue: queueHealth ? 'ok' : 'error',
    },
  })
})



// Global error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', error);

  // Handle specific error types
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.message,
      },
    });
  }

  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Invalid token',
      },
    });
  }

  // Default error response
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
    },
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'Endpoint not found',
    },
  });
});

export default app
