import Bull from 'bull';
import { RedisService } from './redis';

interface ImageProcessingJobData {
  imageId: string;
  userId: string;
  s3Key: string;
  s3Bucket: string;
  mode: string;
  settings?: any;
}

interface EmailJobData {
  to: string;
  subject: string;
  template: string;
  data: any;
}

interface TokenDeductionJobData {
  userId: string;
  amount: number;
  description: string;
  resourceId?: string;
}

class QueueServiceClass {
  private imageProcessingQueue: Bull.Queue<ImageProcessingJobData> | null = null;
  private emailQueue: Bull.Queue<EmailJobData> | null = null;
  private tokenQueue: Bull.Queue<TokenDeductionJobData> | null = null;
  private isInitialized: boolean = false;

  async initialize(): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      
      // Initialize queues
      this.imageProcessingQueue = new Bull('image-processing', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      });

      this.emailQueue = new Bull('email', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 10,
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        },
      });

      this.tokenQueue = new Bull('token-deduction', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 20,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 500,
          },
        },
      });

      // Set up job processors
      this.setupJobProcessors();

      this.isInitialized = true;
      console.log('✓ Queue service initialized successfully');

    } catch (error) {
      console.error('Failed to initialize queue service:', error);
      // Don't throw error in development
      if (process.env.NODE_ENV === 'production') {
        throw error;
      }
    }
  }

  private setupJobProcessors(): void {
    // Image processing job processor
    this.imageProcessingQueue?.process(async (job) => {
      const { imageId, userId, s3Key, mode, settings } = job.data;
      
      console.log(`Processing image ${imageId} for user ${userId}`);
      
      try {
        // Update job progress
        await job.progress(10);
        
        // Simulate image processing (replace with actual processing logic)
        await this.simulateImageProcessing(job);
        
        // Update database with processing results
        await this.updateImageProcessingResults(imageId, {
          status: 'COMPLETED',
          processedAt: new Date(),
          results: {
            mode,
            settings,
            // Add actual processing results here
          }
        });

        console.log(`Image processing completed for ${imageId}`);
        return { success: true, imageId };

      } catch (error) {
        console.error(`Image processing failed for ${imageId}:`, error);
        
        // Update database with error status
        await this.updateImageProcessingResults(imageId, {
          status: 'FAILED',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        
        throw error;
      }
    });

    // Email job processor
    this.emailQueue?.process(async (job) => {
      const { to, subject, template, data } = job.data;
      
      console.log(`Sending email to ${to}: ${subject}`);
      
      try {
        // Simulate email sending (replace with actual email service)
        await this.simulateEmailSending(job);
        
        console.log(`Email sent successfully to ${to}`);
        return { success: true, to };

      } catch (error) {
        console.error(`Email sending failed to ${to}:`, error);
        throw error;
      }
    });

    // Token deduction job processor
    this.tokenQueue?.process(async (job) => {
      const { userId, amount, description, resourceId } = job.data;
      
      console.log(`Processing token deduction for user ${userId}: ${amount} tokens`);
      
      try {
        // Deduct tokens from user account
        await this.processTokenDeduction(userId, amount, description, resourceId);
        
        console.log(`Token deduction completed for user ${userId}`);
        return { success: true, userId, amount };

      } catch (error) {
        console.error(`Token deduction failed for user ${userId}:`, error);
        throw error;
      }
    });
  }

  // Job creation methods
  async addImageProcessingJob(data: ImageProcessingJobData, options?: Bull.JobOptions): Promise<Bull.Job<ImageProcessingJobData> | null> {
    try {
      if (!this.imageProcessingQueue) {
        console.warn('Image processing queue not available');
        return null;
      }

      const job = await this.imageProcessingQueue.add('process-image', data, {
        priority: 1,
        delay: 0,
        ...options,
      });

      console.log(`Image processing job created: ${job.id}`);
      return job;

    } catch (error) {
      console.error('Failed to add image processing job:', error);
      return null;
    }
  }

  async addEmailJob(data: EmailJobData, options?: Bull.JobOptions): Promise<Bull.Job<EmailJobData> | null> {
    try {
      if (!this.emailQueue) {
        console.warn('Email queue not available');
        return null;
      }

      const job = await this.emailQueue.add('send-email', data, {
        priority: 2,
        ...options,
      });

      console.log(`Email job created: ${job.id}`);
      return job;

    } catch (error) {
      console.error('Failed to add email job:', error);
      return null;
    }
  }

  async addTokenDeductionJob(data: TokenDeductionJobData, options?: Bull.JobOptions): Promise<Bull.Job<TokenDeductionJobData> | null> {
    try {
      if (!this.tokenQueue) {
        console.warn('Token queue not available');
        return null;
      }

      const job = await this.tokenQueue.add('deduct-tokens', data, {
        priority: 3,
        ...options,
      });

      console.log(`Token deduction job created: ${job.id}`);
      return job;

    } catch (error) {
      console.error('Failed to add token deduction job:', error);
      return null;
    }
  }

  // Queue status methods
  async getQueueStats() {
    try {
      const [imageStats, emailStats, tokenStats] = await Promise.all([
        this.imageProcessingQueue?.getJobCounts(),
        this.emailQueue?.getJobCounts(),
        this.tokenQueue?.getJobCounts(),
      ]);

      return {
        imageProcessing: imageStats || {},
        email: emailStats || {},
        tokenDeduction: tokenStats || {},
      };
    } catch (error) {
      console.error('Failed to get queue stats:', error);
      return {};
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        return false;
      }

      // Check if queues are responsive
      const stats = await this.getQueueStats();
      return Object.keys(stats).length > 0;
    } catch (error) {
      console.error('Queue health check failed:', error);
      return false;
    }
  }

  // Helper methods with actual implementation
  private async simulateImageProcessing(job: Bull.Job): Promise<void> {
    const { imageId, mode, settings } = job.data;

    // Step 1: Initialize processing
    await job.progress(10);
    console.log(`Starting ${mode} processing for image ${imageId}`);

    // Step 2: Load and validate image
    await job.progress(25);
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log(`Image loaded and validated: ${imageId}`);

    // Step 3: Apply processing based on mode
    await job.progress(50);
    await new Promise(resolve => setTimeout(resolve, 1000));

    switch (mode) {
      case 'sketch':
        console.log(`Applying sketch conversion to ${imageId}`);
        break;
      case 'cartoon':
        console.log(`Applying cartoon conversion to ${imageId}`);
        break;
      case 'anime':
        console.log(`Applying anime conversion to ${imageId}`);
        break;
      default:
        console.log(`Applying default processing to ${imageId}`);
    }

    // Step 4: Post-processing and optimization
    await job.progress(75);
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log(`Post-processing completed for ${imageId}`);

    // Step 5: Save results
    await job.progress(90);
    await new Promise(resolve => setTimeout(resolve, 300));
    console.log(`Results saved for ${imageId}`);

    await job.progress(100);
  }

  private async simulateEmailSending(job: Bull.Job): Promise<void> {
    const { to, template } = job.data;

    // Simulate email template processing
    await new Promise(resolve => setTimeout(resolve, 200));
    console.log(`Processing ${template} template for ${to}`);

    // Simulate email delivery
    await new Promise(resolve => setTimeout(resolve, 300));
    console.log(`Email delivered to ${to}`);
  }

  private async updateImageProcessingResults(imageId: string, results: any): Promise<void> {
    try {
      // Import DatabaseService dynamically to avoid circular dependencies
      const { DatabaseService } = await import('./database');

      // Update image status in database
      await DatabaseService.client.image.update({
        where: { id: imageId },
        data: {
          status: results.status,
          processedAt: results.processedAt,
          processingResults: results.results ? JSON.stringify(results.results) : null,
          error: results.error || null,
        }
      });

      console.log(`Database updated for image ${imageId}: ${results.status}`);
    } catch (error) {
      console.error(`Failed to update database for image ${imageId}:`, error);
      throw error;
    }
  }

  private async processTokenDeduction(userId: string, amount: number, description: string, resourceId?: string): Promise<void> {
    try {
      // Import DatabaseService dynamically to avoid circular dependencies
      const { DatabaseService } = await import('./database');

      // Create token transaction
      await DatabaseService.client.tokenTransaction.create({
        data: {
          userId,
          amount: -Math.abs(amount), // Ensure negative for deduction
          type: 'DEDUCTION',
          description,
          resourceId,
        }
      });

      // Update user token balance
      await DatabaseService.client.user.update({
        where: { id: userId },
        data: {
          tokenBalance: {
            decrement: Math.abs(amount)
          }
        }
      });

      console.log(`Deducted ${amount} tokens from user ${userId}: ${description}`);
    } catch (error) {
      console.error(`Failed to process token deduction for user ${userId}:`, error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await Promise.all([
        this.imageProcessingQueue?.close(),
        this.emailQueue?.close(),
        this.tokenQueue?.close(),
      ]);
      
      this.isInitialized = false;
      console.log('Queue service disconnected');
    } catch (error) {
      console.error('Failed to disconnect queue service:', error);
    }
  }
}

export const QueueService = new QueueServiceClass();
